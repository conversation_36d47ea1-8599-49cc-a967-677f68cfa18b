use crate::database::DatabasePool;
use crate::models::{Post, PostStatus, CreatePostRequest, UpdatePostRequest, PostListQuery};
use crate::utils::error::{AppError, Result};
use sqlx::Row;

pub struct PostRepository;

impl PostRepository {
    pub async fn create(pool: &DatabasePool, request: CreatePostRequest) -> Result<Post> {
        let post_images_json = request.post_images
            .map(|images| serde_json::to_string(&images))
            .transpose()?;

        let status = request.status.unwrap_or(PostStatus::Draft);

        let row = sqlx::query!(
            r#"
            INSERT INTO posts (title, cover_url, content, category_id, status, post_images)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING id, title, cover_url, content, category_id, status, post_images, created_at, updated_at
            "#,
            request.title,
            request.cover_url,
            request.content,
            request.category_id,
            status as i32,
            post_images_json
        )
        .fetch_one(pool)
        .await?;

        Ok(Post {
            id: row.id,
            title: row.title,
            cover_url: row.cover_url,
            content: row.content,
            category_id: row.category_id,
            status: PostStatus::from(row.status),
            post_images: row.post_images,
            created_at: row.created_at,
            updated_at: row.updated_at,
        })
    }

    pub async fn get_by_id(pool: &DatabasePool, id: i64) -> Result<Option<Post>> {
        let row = sqlx::query_as!(
            Post,
            r#"
            SELECT id, title, cover_url, content, category_id, status as "status: i32", post_images, created_at, updated_at
            FROM posts
            WHERE id = ? AND status != ?
            "#,
            id,
            PostStatus::Deleted as i32
        )
        .fetch_optional(pool)
        .await?;

        Ok(row.map(|mut post| {
            post.status = PostStatus::from(post.status as i32);
            post
        }))
    }

    pub async fn list(pool: &DatabasePool, query: PostListQuery) -> Result<(Vec<Post>, i64)> {
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        let mut where_conditions = vec!["status != ?".to_string()];
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = vec![
            Box::new(PostStatus::Deleted as i32)
        ];

        if let Some(category_id) = query.category_id {
            where_conditions.push("category_id = ?".to_string());
            params.push(Box::new(category_id));
        }

        if let Some(status) = query.status {
            where_conditions.push("status = ?".to_string());
            params.push(Box::new(status as i32));
        }

        let where_clause = where_conditions.join(" AND ");

        // Get total count
        let count_query = format!("SELECT COUNT(*) as count FROM posts WHERE {}", where_clause);
        let total: i64 = sqlx::query(&count_query)
            .bind(PostStatus::Deleted as i32)
            .fetch_one(pool)
            .await?
            .get("count");

        // Get posts
        let posts_query = format!(
            "SELECT id, title, cover_url, content, category_id, status, post_images, created_at, updated_at 
             FROM posts WHERE {} ORDER BY created_at DESC LIMIT ? OFFSET ?",
            where_clause
        );

        let rows = sqlx::query(&posts_query)
            .bind(PostStatus::Deleted as i32)
            .bind(page_size as i64)
            .bind(offset as i64)
            .fetch_all(pool)
            .await?;

        let posts: Vec<Post> = rows
            .into_iter()
            .map(|row| Post {
                id: row.get("id"),
                title: row.get("title"),
                cover_url: row.get("cover_url"),
                content: row.get("content"),
                category_id: row.get("category_id"),
                status: PostStatus::from(row.get::<i32, _>("status")),
                post_images: row.get("post_images"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            })
            .collect();

        Ok((posts, total))
    }

    pub async fn update(pool: &DatabasePool, id: i64, request: UpdatePostRequest) -> Result<Option<Post>> {
        let existing = Self::get_by_id(pool, id).await?;
        if existing.is_none() {
            return Ok(None);
        }

        let post_images_json = request.post_images
            .map(|images| serde_json::to_string(&images))
            .transpose()?;

        let row = sqlx::query!(
            r#"
            UPDATE posts 
            SET title = COALESCE(?, title),
                cover_url = COALESCE(?, cover_url),
                content = COALESCE(?, content),
                category_id = COALESCE(?, category_id),
                status = COALESCE(?, status),
                post_images = COALESCE(?, post_images),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            RETURNING id, title, cover_url, content, category_id, status, post_images, created_at, updated_at
            "#,
            request.title,
            request.cover_url,
            request.content,
            request.category_id,
            request.status.map(|s| s as i32),
            post_images_json,
            id
        )
        .fetch_one(pool)
        .await?;

        Ok(Some(Post {
            id: row.id,
            title: row.title,
            cover_url: row.cover_url,
            content: row.content,
            category_id: row.category_id,
            status: PostStatus::from(row.status),
            post_images: row.post_images,
            created_at: row.created_at,
            updated_at: row.updated_at,
        }))
    }

    pub async fn delete(pool: &DatabasePool, id: i64) -> Result<bool> {
        let result = sqlx::query!(
            "UPDATE posts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            PostStatus::Deleted as i32,
            id
        )
        .execute(pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }
}
